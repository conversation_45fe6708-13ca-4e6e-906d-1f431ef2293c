#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第四批测试API清单
专门用于第四批补充测试API的性能测试
包括：会员系统、论坛系统、医案系统、症状管理、预后系统、邀请系统、其他功能API
"""

# 第四批测试API列表 - 补充之前遗漏的API（已移除高风险API）
FOURTH_BATCH_APIS = [
    # 会员系统API (1个) - 移除高风险API
    "记录目标",     # 跳过：AbstractGoalRecordView有同步异步混用问题
    "自定义活动",   # 跳过：同步异步混用问题
    "用户卡片",     # 跳过：同步异步混用问题
    "日活动",
    "用户问答历史",

    # 论坛系统API (3个)
    "创建帖子",
    "搜索帖子",
    "帖子评论",

    # 医案系统API (7个) - 2个中风险API降低并发
    "时间效率排行",
    "提交游戏记录",    # 中风险：限制并发≤5
    "增加经验值",      # 中风险：限制并发≤5
    "经验值历史",
    "游戏历史记录",
    "案例排行榜",
    "平均得分排行",

    # 症状管理API (1个) - 已修复异步
    "添加自定义症状",  # 已修复：异步版本

    # 预后系统API (1个)
    "执行搜索",

    # 邀请系统API (2个)
    "我的邀请码",
    "邀请记录",

    # 其他功能API (3个)
    "系统心跳",
    "干支查询",
    "获取当前季节"
]

# 按业务模块分组
FOURTH_BATCH_BY_MODULE = {
    "会员系统": [
        "记录目标",     # 跳过：同步异步混用问题
        "自定义活动",   # 跳过：同步异步混用问题
        "用户卡片",     # 跳过：同步异步混用问题
        "日活动",
        "用户问答历史"
    ],
    "论坛系统": [
        "创建帖子",
        "搜索帖子",
        "帖子评论"
    ],
    "医案系统": [
        "时间效率排行",
        "提交游戏记录",
        "增加经验值",
        "经验值历史",
        "游戏历史记录",
        "案例排行榜",
        "平均得分排行"
    ],
    "症状管理": [
        "添加自定义症状"   # 已修复：异步版本
    ],
    "预后系统": [
        "执行搜索"
    ],
    "邀请系统": [
        "我的邀请码",
        "邀请记录"
    ],
    "其他功能": [
        "系统心跳",
        "干支查询",
        "获取当前季节"
    ]
}

# 按风险等级分组
FOURTH_BATCH_BY_RISK = {
    "低风险": [
        "日活动",
        "用户卡片",
        "用户问答历史",
        "搜索帖子",
        "帖子评论",
        "时间效率排行",
        "经验值历史",
        "游戏历史记录",
        "案例排行榜",
        "平均得分排行",
        "执行搜索",
        "我的邀请码",
        "邀请记录",
        "系统心跳",
        "干支查询",
        "获取当前季节"
    ],
    "中风险": [
        "记录目标",  # 跳过：同步异步混用问题
        "自定义活动",
        "创建帖子",
        "提交游戏记录",
        "增加经验值",
        "添加症状",
        "更新症状",
        "添加自定义症状"
    ]
}

# 按API类型分组
FOURTH_BATCH_BY_TYPE = {
    "读取操作": [
        "日活动",
        "用户卡片",
        "用户问答历史",
        "搜索帖子",
        "帖子评论",
        "时间效率排行",
        "经验值历史",
        "游戏历史记录",
        "案例排行榜",
        "平均得分排行",
        "我的邀请码",
        "邀请记录",
        "系统心跳",
        "干支查询",
        "获取当前季节"
    ],
    "写入操作": [
        # "记录目标",  # 跳过：同步异步混用问题
        "自定义活动",
        "创建帖子",
        "提交游戏记录",
        "增加经验值",
        "添加症状",
        "更新症状",
        "添加自定义症状"
    ],
    "搜索操作": [
        "执行搜索",
        "搜索帖子"
    ]
}

def print_fourth_batch_summary():
    """打印第四批测试API汇总信息"""
    print("第四批测试API清单:")
    print("=" * 60)
    
    print(f"\n📊 总体统计:")
    print(f"   总API数量: {len(FOURTH_BATCH_APIS)} 个")
    print(f"   低风险API: {len(FOURTH_BATCH_BY_RISK['低风险'])} 个")
    print(f"   中风险API: {len(FOURTH_BATCH_BY_RISK['中风险'])} 个")
    print(f"   读取操作: {len(FOURTH_BATCH_BY_TYPE['读取操作'])} 个")
    print(f"   写入操作: {len(FOURTH_BATCH_BY_TYPE['写入操作'])} 个")
    print(f"   搜索操作: {len(FOURTH_BATCH_BY_TYPE['搜索操作'])} 个")
    print(f"   ⚠️ 跳过API: 1个 (记录目标 - 同步异步混用问题)")
    
    print(f"\n📋 按业务模块分组:")
    for module, apis in FOURTH_BATCH_BY_MODULE.items():
        print(f"\n🔹 {module} ({len(apis)}个API):")
        for i, api in enumerate(apis, 1):
            risk = "中风险" if api in FOURTH_BATCH_BY_RISK['中风险'] else "低风险"
            api_type = "写入" if api in FOURTH_BATCH_BY_TYPE['写入操作'] else "读取"
            if api in FOURTH_BATCH_BY_TYPE['搜索操作']:
                api_type = "搜索"
            print(f"   {i:2d}. {api:<20} [{risk}] [{api_type}]")

    print(f"\n⚠️ 测试建议:")
    print(f"   • 低风险API可以正常并发测试")
    print(f"   • 中风险API建议低并发测试（≤15并发）")
    print(f"   • 写入操作API需要控制测试数据量")
    print(f"   • 搜索操作API注意缓存命中率测试")

if __name__ == "__main__":
    print_fourth_batch_summary()
